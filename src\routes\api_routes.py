"""
API routes for web interface and external integrations.
"""

import requests
from flask import request, jsonify

from ..core.database import collection
from ..core.config import COLLECTION_NAME


def get_stats(app):
    """Get collection statistics."""
    try:
        all_data = collection.get(include=[])
        total_docs = len(all_data.get("ids", []))

        return jsonify({
            "status": "ok",
            "total_documents": total_docs,
            "collection_name": COLLECTION_NAME
        })
    except Exception as e:
        app.logger.error(f"Stats operation failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def get_all_documents(app):
    """Get all documents with pagination support."""
    try:
        results = collection.get(include=["documents", "metadatas"])

        app.logger.info(f"ChromaDB get() returned: {type(results)}")
        app.logger.info(f"Results keys: {list(results.keys()) if isinstance(results, dict) else 'Not a dict'}")

        # Handle case where collection is empty
        if not results or not results.get("ids"):
            app.logger.info("Collection is empty, returning empty document list")
            return jsonify({
                "status": "ok",
                "documents": [],
                "total": 0
            })

        # Process results
        documents = []
        ids = results.get("ids", [])
        docs = results.get("documents", [])
        metadatas = results.get("metadatas", [])

        for i, doc_id in enumerate(ids):
            doc_content = docs[i] if i < len(docs) else ""
            metadata = metadatas[i] if i < len(metadatas) else {}
            
            documents.append({
                "id": doc_id,
                "document": doc_content,
                "metadata": metadata
            })

        return jsonify({
            "status": "ok",
            "documents": documents,
            "total": len(documents)
        })

    except Exception as e:
        app.logger.error(f"Get documents failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def add_document_api(app):
    """Add a single document (enhanced version of /add)."""
    try:
        data = request.get_json(force=True)

        ids = data.get("ids", [])
        documents = data.get("documents", [])
        metadatas = data.get("metadatas", [])
        embeddings = data.get("embeddings")

        if not (ids and documents):
            return jsonify({"status": "error", "error": "ids and documents required"}), 400

        add_params = {
            "ids": ids,
            "documents": documents,
            "metadatas": metadatas
        }

        if embeddings:
            add_params["embeddings"] = embeddings

        collection.add(**add_params)

        app.logger.info(f"Added document(s): {ids}")
        return jsonify({"status": "ok", "inserted": len(ids), "ids": ids})

    except Exception as e:
        app.logger.error(f"Add document failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def delete_document_api(app):
    """Delete a document by ID."""
    try:
        data = request.get_json(force=True)
        doc_id = data.get("id")

        if not doc_id:
            return jsonify({"status": "error", "error": "id required"}), 400

        collection.delete(ids=[doc_id])
        app.logger.info(f"Deleted document: {doc_id}")
        return jsonify({"status": "ok", "deleted": doc_id})

    except Exception as e:
        app.logger.error(f"Delete document failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def delete_documents_by_source(app):
    """Delete all documents from a specific source file."""
    try:
        data = request.get_json(force=True)
        source = data.get("source")

        if not source:
            return jsonify({"status": "error", "error": "source required"}), 400

        app.logger.info(f"Attempting to delete documents from source: {source}")

        # First, find all documents with the matching source
        try:
            # Query for documents with matching Source metadata
            results = collection.get(
                where={"Source": source},
                include=["metadatas"]
            )

            if not results or not results.get("ids"):
                app.logger.info(f"No documents found for source: {source}")
                return jsonify({
                    "status": "error",
                    "error": "Source not found"
                }), 400

            # Get the IDs of documents to delete
            doc_ids = results["ids"]
            deleted_count = len(doc_ids)

            app.logger.info(f"Found {deleted_count} documents to delete from source: {source}")

            # Delete all matching documents
            collection.delete(ids=doc_ids)

            app.logger.info(f"Successfully deleted {deleted_count} documents from source: {source}")

            return jsonify({
                "status": "success",
                "message": f"Successfully deleted source '{source}'",
                "deleted_count": deleted_count,
                "source": source
            })

        except Exception as query_error:
            app.logger.error(f"Error querying documents for source {source}: {query_error}")
            return jsonify({
                "status": "error",
                "error": "Source not found"
            }), 400

    except Exception as e:
        app.logger.error(f"Delete documents by source failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def _parse_query_for_source_filter(query):
    """
    Parse query to extract source filter if present.
    
    Supports format: @filename.pdf rest of query
    
    Returns:
        tuple: (source_filter, cleaned_query) or (None, original_query)
    """
    if not query or not isinstance(query, str):
        return None, query
    
    query = query.strip()
    
    # Check if query starts with @filename pattern
    if query.startswith('@'):
        # Find the first space to separate the source from the rest of the query
        space_index = query.find(' ')
        if space_index > 1:  # Ensure there's content after @
            source_part = query[1:space_index]  # Remove @ prefix
            cleaned_query = query[space_index + 1:].strip()  # Remove source and leading spaces
            
            # Validate that source_part looks like a filename (contains a dot)
            if '.' in source_part:
                return source_part, cleaned_query
    
    # No valid source filter found, return original query
    return None, query


def search_documents(app):
    """Search documents by text query using Ollama embeddings with optional source filtering."""
    try:
        data = request.get_json(force=True)
        original_query = data.get("query", "")
        n_results = data.get("n_results", 10)

        if not original_query:
            return jsonify({"status": "error", "error": "query required"}), 400

        # Parse query for source filter
        source_filter, cleaned_query = _parse_query_for_source_filter(original_query)

        app.logger.info(f"Original query: '{original_query}'")
        if source_filter:
            app.logger.info(f"Source filter: '{source_filter}', cleaned query: '{cleaned_query}'")
        else:
            app.logger.info("No source filter detected, searching all documents")

        # Generate embedding for the cleaned query (without source prefix)
        query_for_embedding = cleaned_query if source_filter else original_query
        app.logger.info(f"Generating embedding for query: '{query_for_embedding}'")

        ollama_payload = {"model": "all-minilm:33m", "prompt": query_for_embedding} # "all-minilm:33m" or "snowflake-artic-embed2:568m"

        ollama_response = requests.post(
            "http://localhost:11434/api/embeddings",
            json=ollama_payload,
            timeout=30
        )

        if ollama_response.status_code != 200:
            app.logger.error(f"Ollama embedding failed: {ollama_response.status_code}")
            return jsonify({"status": "error", "error": "Failed to generate embedding"}), 500

        embedding_data = ollama_response.json()
        query_embedding = embedding_data.get("embedding")

        if not query_embedding:
            app.logger.error("No embedding returned from Ollama")
            return jsonify({"status": "error", "error": "No embedding generated"}), 500

        app.logger.info(f"Generated embedding with {len(query_embedding)} dimensions")

        # Use intelligent search process with keyword filtering
        from ..flows.query import _intelligent_search_process

        app.logger.info("Using intelligent search process with keyword filtering")
        results = _intelligent_search_process(query_for_embedding, query_embedding, source_filter)

        # Format results
        formatted_results = []
        if results and results.get("ids") and results["ids"][0]:
            for i in range(len(results["ids"][0])):
                formatted_results.append({
                    "id": results["ids"][0][i],
                    "content": results["documents"][0][i][:300] + "..." if len(results["documents"][0][i]) > 300 else results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "distance": results["distances"][0][i]
                })

        app.logger.info(f"Search completed: {len(formatted_results)} results")

        return jsonify({
            "status": "ok",
            "results": formatted_results,
            "total": len(formatted_results),
            "query_info": {
                "original_query": original_query,
                "processed_query": query_for_embedding,
                "source_filter": source_filter
            }
        })

    except Exception as e:
        app.logger.error(f"Search failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def get_collection_info(app):
    """Get detailed collection information."""
    try:
        all_data = collection.get(include=["embeddings"])
        count = len(all_data.get("ids", []))

        # Check embedding dimensions if we have data
        embedding_dimension = None
        if all_data.get("embeddings") and len(all_data["embeddings"]) > 0:
            embedding_dimension = len(all_data["embeddings"][0])

        return jsonify({
            "status": "ok",
            "name": COLLECTION_NAME,
            "count": count,
            "client_type": "PersistentClient",
            "path": "./chroma_data",
            "embedding_dimension": embedding_dimension
        })
    except Exception as e:
        app.logger.error(f"Collection info failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def health_check(app):
    """Health check endpoint."""
    try:
        # Test ChromaDB connection
        collection.count()
        return jsonify({
            "status": "healthy",
            "service": "ChromaDB Server",
            "database": "connected"
        })
    except Exception as e:
        app.logger.error(f"Health check failed: {e}")
        return jsonify({
            "status": "unhealthy",
            "service": "ChromaDB Server",
            "database": "disconnected",
            "error": str(e)
        }), 500
