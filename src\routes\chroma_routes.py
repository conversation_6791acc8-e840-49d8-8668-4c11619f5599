"""
ChromaDB operations and query routes.
"""

import json
from flask import request, jsonify

from ..core.utils import create_error_response, create_success_response
from ..core.database import collection
from ..core.config import COLLECTION_NAME
from ..flows.query import _intelligent_search_process, _clear_request_cache
from ..core.exceptions import OllamaServiceError


def _parse_json_field(field_value, field_name):
    """
    Parse a field that might be a JSON string (common with n8n).

    Args:
        field_value: The field value to parse
        field_name: Name of the field for logging

    Returns:
        Parsed value or original value if not a JSON string
    """
    if isinstance(field_value, str):
        try:
            parsed_value = json.loads(field_value)
            return parsed_value
        except json.JSONDecodeError:
            # If it's not valid JSON, return as-is (might be a simple string)
            return field_value
    return field_value


def _process_and_validate_metadata(metadatas, expected_count):
    """
    Process and validate metadata to ensure it contains required fields and proper structure.

    Args:
        metadatas: Raw metadata from request (list or single dict)
        expected_count: Expected number of metadata objects

    Returns:
        List of processed metadata dictionaries
    """
    if not metadatas:
        return [{}] * expected_count

    # Ensure metadatas is a list
    if isinstance(metadatas, dict):
        metadatas = [metadatas]

    processed_metadatas = []

    for i, metadata in enumerate(metadatas):
        if not isinstance(metadata, dict):
            metadata = {}

        # Create a copy to avoid modifying original
        processed_metadata = metadata.copy()

        # Validate and set default values for expected fields
        # Source field (required for filtering functionality)
        if "Source" not in processed_metadata or not processed_metadata["Source"]:
            processed_metadata["Source"] = "unknown"

        # Title field (optional, for enhanced search)
        if "Title" not in processed_metadata:
            processed_metadata["Title"] = ""

        # Remark field (optional, for additional context)
        if "Remark" not in processed_metadata:
            processed_metadata["Remark"] = ""

        processed_metadatas.append(processed_metadata)

    # If we have fewer metadata objects than expected, duplicate the last one
    while len(processed_metadatas) < expected_count:
        if processed_metadatas:
            processed_metadatas.append(processed_metadatas[-1].copy())
        else:
            processed_metadatas.append({"Source": "unknown", "Title": "", "Remark": ""})

    # If we have more metadata objects than expected, truncate
    if len(processed_metadatas) > expected_count:
        processed_metadatas = processed_metadatas[:expected_count]

    return processed_metadatas


def _get_metadata_summary(metadatas):
    """
    Generate a summary of metadata fields for logging and response.

    Args:
        metadatas: List of processed metadata dictionaries

    Returns:
        Dictionary with metadata field statistics
    """
    if not metadatas:
        return {"total_count": 0, "fields": []}

    # Count field usage
    field_counts = {}
    sources = set()

    for metadata in metadatas:
        for field, value in metadata.items():
            if field not in field_counts:
                field_counts[field] = {"present": 0, "non_empty": 0}

            field_counts[field]["present"] += 1
            if value and str(value).strip():
                field_counts[field]["non_empty"] += 1

            if field == "Source" and value:
                sources.add(value)

    return {
        "total_count": len(metadatas),
        "unique_sources": len(sources),
        "field_statistics": field_counts,
        "sources": list(sources)[:5]  # Show first 5 sources
    }


def add_to_chroma(app):
    """Add documents to ChromaDB collection with enhanced metadata support."""
    try:
        data = request.get_json(force=True)
        if not data:
            return create_error_response("No JSON data provided", 400, "validation_error")

        app.logger.info(f"Add request received with keys: {list(data.keys())}")
        app.logger.info(f"Raw request data keys and types: {[(k, type(v)) for k, v in data.items()]}")

        # Parse input fields that might be JSON strings (common with n8n)
        ids = _parse_json_field(data.get("ids"), "ids")
        documents = _parse_json_field(data.get("documents"), "documents")
        embeddings = data.get("embedding")  # embeddings are usually already parsed correctly

        # Handle both 'metadatas' and 'metedatas' (typo in n8n)
        raw_metadatas = data.get("metadatas") or data.get("metedatas")
        raw_metadatas = _parse_json_field(raw_metadatas, "metadatas")

        # Convert single metadata object to list if needed
        metadatas = [raw_metadatas] if isinstance(raw_metadatas, dict) else raw_metadatas

        # Normalize ids and documents to lists if they're single strings
        if isinstance(ids, str):
            ids = [ids]
            app.logger.info("Converted single ID string to list")

        if isinstance(documents, str):
            documents = [documents]
            app.logger.info("Converted single document string to list")

        # Enhanced logging for debugging
        app.logger.info(f"IDs type: {type(ids)}, count: {len(ids) if isinstance(ids, list) else 'N/A'}")
        app.logger.info(f"Documents type: {type(documents)}, count: {len(documents) if isinstance(documents, list) else 'N/A'}")
        app.logger.info(f"Embeddings type: {type(embeddings)}, count: {len(embeddings) if isinstance(embeddings, list) else 'N/A'}")
        app.logger.info(f"Metadatas type: {type(metadatas)}, count: {len(metadatas) if isinstance(metadatas, list) else 'N/A'}")

        if isinstance(ids, list) and len(ids) > 0:
            app.logger.info(f"First few IDs: {ids[:3]}")

        if isinstance(documents, list) and len(documents) > 0:
            app.logger.info(f"First document length: {len(documents[0]) if documents[0] else 0} chars")

        if isinstance(embeddings, list) and len(embeddings) > 0:
            app.logger.info(f"First embedding type: {type(embeddings[0])}")
            if isinstance(embeddings[0], list):
                app.logger.info(f"First embedding dimension: {len(embeddings[0])}")

        if isinstance(metadatas, list) and len(metadatas) > 0:
            app.logger.info(f"First metadata: {metadatas[0]}")
        elif metadatas:
            app.logger.info(f"Metadatas (non-list): {metadatas}")

        # Validate required fields
        if not (ids and documents and embeddings):
            return create_error_response(
                "ids, documents, and embeddings are required",
                400,
                "validation_error"
            )

        # Validate and normalize array lengths
        ids_count = len(ids) if isinstance(ids, list) else 1
        docs_count = len(documents) if isinstance(documents, list) else 1

        # Handle embeddings - check if it's a 2D array (list of vectors) or 1D array (single flattened vector)
        if isinstance(embeddings, list):
            if len(embeddings) > 0 and isinstance(embeddings[0], list):
                # 2D array - list of embedding vectors
                embeddings_count = len(embeddings)
                app.logger.info(f"Embeddings: 2D array with {embeddings_count} vectors of dimension {len(embeddings[0])}")
            else:
                # 1D array - single flattened embedding vector, treat as single embedding
                embeddings_count = 1
                embeddings = [embeddings]  # Convert to 2D array
                app.logger.info(f"Embeddings: 1D array with {len(embeddings[0])} dimensions, converted to single vector")
        else:
            embeddings_count = 1
            app.logger.info(f"Embeddings: Non-list type {type(embeddings)}")

        app.logger.info(f"Array lengths - IDs: {ids_count}, Documents: {docs_count}, Embeddings: {embeddings_count}")

        if not (ids_count == docs_count == embeddings_count):
            return create_error_response(
                f"Array length mismatch: ids({ids_count}), documents({docs_count}), embeddings({embeddings_count}) must be equal",
                400,
                "validation_error"
            )

        # Validate and enhance metadata structure
        processed_metadatas = _process_and_validate_metadata(metadatas, ids_count)

        collection.add(
            ids=ids,
            documents=documents,
            embeddings=embeddings,
            metadatas=processed_metadatas
        )

        app.logger.info(f"Successfully added {ids_count} documents to ChromaDB")
        return create_success_response({
            "inserted": ids_count,
            "ids": ids if isinstance(ids, list) else [ids],
            "metadata_fields_processed": _get_metadata_summary(processed_metadatas)
        }, f"Added {ids_count} documents")

    except Exception as e:
        app.logger.error(f"Add operation failed: {e}")
        return create_error_response(f"Failed to add documents: {str(e)}", 500)


def _parse_embedding_input(data):
    """Parse and validate embedding input from various formats."""
    embedding = None

    if isinstance(data, dict):
        embedding = data.get("embedding")
    elif isinstance(data, list):
        embedding = data
    else:
        raise ValueError("Invalid data format")

    if not embedding:
        raise ValueError("embedding required")

    # Handle string array format: "[Array: [0.08345413208007812,...]]"
    if isinstance(embedding, str) and embedding.startswith("[Array: [") and embedding.endswith("]]"):
        try:
            array_content = embedding[8:-2]
            embedding = [float(x.strip()) for x in array_content.split(',')]
        except (ValueError, IndexError) as e:
            raise ValueError(f"Invalid array format in embedding: {e}")

    # Handle n8n string format: "[0.1, 0.2, 0.3, ...]" or "[-0.1, 0.2, -0.3, ...]"
    elif isinstance(embedding, str) and embedding.startswith("[") and embedding.endswith("]"):
        try:
            # Remove brackets and split by comma
            array_content = embedding[1:-1]
            embedding = [float(x.strip()) for x in array_content.split(',')]
        except (ValueError, IndexError) as e:
            raise ValueError(f"Invalid n8n string array format: {e}")

    # Handle comma-separated string format (common from n8n)
    elif isinstance(embedding, str) and ',' in embedding:
        try:
            # Split by comma and convert to floats
            embedding = [float(x.strip()) for x in embedding.split(',')]
        except (ValueError, IndexError) as e:
            raise ValueError(f"Invalid comma-separated embedding format: {e}")

    # Handle JSON string format
    elif isinstance(embedding, str):
        try:
            embedding = json.loads(embedding)
        except json.JSONDecodeError:
            raise ValueError("embedding must be a valid JSON array")

    if not isinstance(embedding, list):
        raise ValueError("embedding must be an array")

    if len(embedding) == 0:
        raise ValueError("embedding array cannot be empty")

    # Validate that all elements are numbers
    for i, val in enumerate(embedding):
        if not isinstance(val, (int, float)):
            raise ValueError(f"embedding[{i}] must be a number, got {type(val)}")

    return embedding


def query_chroma(app):
    """Intelligent query with Ollama-powered semantic understanding and enhanced metadata filtering."""
    try:
        # Phase 1: Request Processing & Validation
        data = request.get_json(force=True)
        app.logger.info(f"Intelligent query request received, data type: {type(data)}")

        if not data:
            return jsonify({
                "status": "error",
                "error": "No JSON data provided",
                "error_type": "validation_error"
            }), 400

        # Extract and validate query parameters - check multiple possible field names
        query_text = data.get("query_text", "").strip()

        # Check for alternative field names that n8n might use
        if not query_text:
            query_text = data.get("query", "").strip()
        if not query_text:
            query_text = data.get("message", "").strip()
        if not query_text:
            query_text = data.get("text", "").strip()
        if not query_text:
            query_text = data.get("user_message", "").strip()
        if not query_text:
            query_text = data.get("question", "").strip()

        if not query_text:
            app.logger.error("No query text found in any expected field")
            app.logger.error(f"Available fields: {list(data.keys())}")
            return jsonify({
                "status": "error",
                "error": "query text is required (check query, query_text, message, text, user_message, or question fields)",
                "error_type": "validation_error"
            }), 400

        app.logger.info(f"Query text: '{query_text}'")

        # Extract source_filter from request data
        source_filter = data.get("source_filter")
        app.logger.info(f"DEBUG: Raw source_filter from request: {repr(source_filter)}")
        app.logger.info(f"DEBUG: source_filter type: {type(source_filter)}")
        app.logger.info(f"DEBUG: source_filter bool: {bool(source_filter)}")
        if source_filter:
            app.logger.info(f"Source filter provided in request: '{source_filter}'")
        else:
            app.logger.info("No source_filter provided in request")

        # Log the request data keys for debugging
        app.logger.info(f"Request data keys: {list(data.keys())}")

        # Log all data values for debugging (truncated)
        for key, value in data.items():
            if key == "embedding":
                app.logger.info(f"  {key}: [embedding array with length {len(value) if isinstance(value, (list, str)) else 'unknown'}]")
            else:
                value_str = str(value)[:100] if len(str(value)) > 100 else str(value)
                app.logger.info(f"  {key}: {repr(value_str)}")

        # Parse and validate embedding
        try:
            embedding = _parse_embedding_input(data)
        except ValueError as e:
            app.logger.error(f"Embedding validation failed: {str(e)}")
            app.logger.error(f"Request data: {data}")
            return jsonify({
                "status": "error",
                "error": str(e),
                "error_type": "validation_error"
            }), 400

        app.logger.info(f"Embedding parsed successfully, dimension: {len(embedding)}")
        app.logger.info(f"First few embedding values: {embedding[:5] if len(embedding) >= 5 else embedding}")

        # Check for embedding dimension mismatch and regenerate if needed
        if len(embedding) == 384:
            app.logger.warning("Received 384-dimensional embedding, but collection expects 1024. Regenerating with snowflake model...")
            try:
                import requests
                ollama_payload = {"model": "snowflake-artic-embed2:568m", "prompt": query_text}
                ollama_response = requests.post(
                    "http://localhost:11434/api/embeddings",
                    json=ollama_payload,
                    timeout=30
                )

                if ollama_response.status_code == 200:
                    ollama_data = ollama_response.json()
                    embedding = ollama_data.get("embedding", [])
                    app.logger.info(f"Successfully regenerated embedding with snowflake model. New dimension: {len(embedding)}")
                else:
                    app.logger.error(f"Failed to regenerate embedding: {ollama_response.status_code}")
                    return jsonify({
                        "status": "error",
                        "error": "Failed to regenerate embedding with correct model",
                        "error_type": "embedding_regeneration_error"
                    }), 500
            except Exception as e:
                app.logger.error(f"Error regenerating embedding: {e}")
                return jsonify({
                    "status": "error",
                    "error": f"Failed to regenerate embedding: {str(e)}",
                    "error_type": "embedding_regeneration_error"
                }), 500

        # Phase 2: Intelligent Query Routing
        from ..flows.query import _detect_source_pattern
        detected_source, cleaned_message = _detect_source_pattern(query_text)
        if detected_source:
            # Case A: Source-specific query (detected from @filename pattern)
            source_filter = detected_source
            query_text = cleaned_message
            app.logger.info(f"Case A: Source-specific query for '{source_filter}' (from @pattern)")
        elif source_filter:
            # Case A: Source-specific query (provided in request)
            app.logger.info(f"Case A: Source-specific query for '{source_filter}' (from request)")
        else:
            # Case B: General query
            source_filter = None
            app.logger.info("Case B: General query")

        # Phase 3: Smart Processing
        app.logger.info("Starting intelligent search process")
        results = _intelligent_search_process(query_text, embedding, source_filter)

        # Phase 4: Response & Logging
        result_count = len(results.get('ids', [[]])[0]) if results.get('ids') else 0
        app.logger.info(f"Intelligent query completed: {result_count} results returned")

        # Clear caches after successful processing
        _clear_request_cache()

        return jsonify({
            "status": "success",
            "results": results,
            "query_info": {
                "original_query": data.get("query", ""),
                "processed_query": query_text,
                "source_filter": source_filter,
                "result_count": result_count
            }
        })

    except OllamaServiceError as e:
        _clear_request_cache()
        app.logger.error(f"Ollama service error: {e}")
        return jsonify({
            "status": "error",
            "error": str(e),
            "error_type": "ollama_service_error"
        }), 503
    except Exception as e:
        _clear_request_cache()
        app.logger.error(f"Intelligent query failed: {e}")
        return jsonify({
            "status": "error",
            "error": str(e),
            "error_type": "internal_error"
        }), 500
